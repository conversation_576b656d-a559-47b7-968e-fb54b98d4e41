import PyPDF2

# 定义文件路径
pdf_path = '李松蔚-心理学通识.pdf'

# 读取PDF文件并打印前几页内容以分析结构
def check_pdf_structure(pdf_path):
    with open(pdf_path, 'rb') as file:
        pdf_reader = PyPDF2.PdfReader(file)
        print(f"PDF共有{len(pdf_reader.pages)}页")
        
        # 打印前5页的内容以分析结构
        for page_num in range(min(5, len(pdf_reader.pages))):
            print(f"\n===== 第{page_num+1}页内容 =====")
            page = pdf_reader.pages[page_num]
            text = page.extract_text()
            print(text[:500])  # 只打印每页前500个字符

if __name__ == "__main__":
    check_pdf_structure(pdf_path)