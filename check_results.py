import pandas as pd
import os

# 检查结果文件
result_file = "生成的封面图片/全部提示词图片生成结果.xlsx"

if os.path.exists(result_file):
    df = pd.read_excel(result_file)
    
    print("🎉 批量生成结果统计")
    print("=" * 50)
    print(f"总提示词数: {len(df)}")
    print(f"成功生成: {df['success'].sum()}")
    print(f"失败数量: {len(df) - df['success'].sum()}")
    print(f"成功率: {df['success'].mean()*100:.1f}%")
    
    print("\n📝 生成的主题列表:")
    for i, row in df.iterrows():
        status = "✅" if row['success'] else "❌"
        print(f"{i+1:2d}. {status} {row['theme']}")
        
    print(f"\n📁 生成的图片文件:")
    success_rows = df[df['success'] == True]
    for i, row in success_rows.iterrows():
        if pd.notna(row['generated_image']):
            filename = os.path.basename(row['generated_image'])
            print(f"   - {filename}")
            
else:
    print("❌ 结果文件不存在")
