import openai
import json
import time
import os
from typing import Dict, Any

class XiaohongshuContentGenerator:
    def __init__(self, api_key: str = None, model: str = "gpt-3.5-turbo"):
        """
        初始化小红书内容生成器
        
        Args:
            api_key: OpenAI API密钥，如果不提供则从环境变量OPENAI_API_KEY获取
            model: 使用的模型，默认为gpt-3.5-turbo
        """
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        self.model = model
        
        if not self.api_key:
            print("警告：未设置OpenAI API密钥，将使用简化版本生成内容")
            self.use_api = False
        else:
            openai.api_key = self.api_key
            self.use_api = True
    
    def generate_content(self, title: str, content: str) -> Dict[str, str]:
        """
        生成小红书风格的内容
        
        Args:
            title: 课程标题
            content: 课程内容
            
        Returns:
            包含封面主标题、副标题、内页文字内容的字典
        """
        if self.use_api:
            return self._generate_with_api(title, content)
        else:
            return self._generate_simple(title, content)
    
    def _generate_with_api(self, title: str, content: str) -> Dict[str, str]:
        """使用API生成内容"""
        try:
            # 构建prompt
            prompt = self._build_prompt(title, content)
            
            # 调用API
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的小红书内容创作者，擅长将心理学知识转化为通俗易懂、吸引人的社交媒体内容。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1500,
                temperature=0.7
            )
            
            # 解析响应
            result_text = response.choices[0].message.content.strip()
            return self._parse_api_response(result_text)
            
        except Exception as e:
            print(f"API调用失败: {e}")
            print("使用简化版本生成内容...")
            return self._generate_simple(title, content)
    
    def _build_prompt(self, title: str, content: str) -> str:
        """构建API调用的prompt"""
        # 截取内容的前800字符作为输入
        content_preview = content[:800] + "..." if len(content) > 800 else content

        # 提取课程主题关键词
        topic = title.split('|')[1] if '|' in title else title
        if '：' in topic:
            topic = topic.split('：')[0]

        prompt = f"""
我是一个小红书心理学图文博主，请基于以下内容做出下处理：

课程标题：{title}
课程内容：{content_preview}

处理要求：
1、提取核心知识点，去掉讲述人口吻和老师个人信息
2、形成结构化小红书知识讲解内容并增加对人的启发
3、生成符合小红书表达的格式和符号等
4、总结封面主标题和副标题

具体要求：

1. 封面主标题（15-25字）：
   - 必须包含吸引眼球的元素
   - 使用疑问句、感叹句或数字
   - 适当使用emoji（💡🧠✨💭等）
   - 要让人一看就想点进去

2. 副标题（20-35字）：
   - 补充说明或提出引人思考的问题
   - 可以暗示内容的实用价值
   - 语气要亲切自然

3. 内页文字内容（480-520字）：
   - 开头要有hook，吸引读者继续阅读
   - 用生活化的例子解释心理学概念
   - 分段清晰，每段2-3句话
   - 包含2-3个实用的心理学知识点
   - 语言要口语化，像和朋友聊天
   - 适当使用emoji增加趣味性（但不要过多）
   - 结尾要有行动建议或互动问题
   - 最后加上相关话题标签（#心理学 #自我成长等）

小红书用户特点：
- 主要是18-35岁的年轻人
- 喜欢实用、有趣的内容
- 注重个人成长和生活品质
- 喜欢分享和互动

请按照以下格式输出：
封面主标题：[内容]
副标题：[内容]
内页文字内容：[内容]
"""
        return prompt
    
    def _parse_api_response(self, response_text: str) -> Dict[str, str]:
        """解析API响应"""
        result = {
            '封面主标题': '',
            '副标题': '',
            '内页文字内容': ''
        }
        
        lines = response_text.split('\n')
        current_key = None
        current_content = []
        
        for line in lines:
            line = line.strip()
            if line.startswith('封面主标题：'):
                if current_key and current_content:
                    result[current_key] = '\n'.join(current_content).strip()
                current_key = '封面主标题'
                current_content = [line.replace('封面主标题：', '').strip()]
            elif line.startswith('副标题：'):
                if current_key and current_content:
                    result[current_key] = '\n'.join(current_content).strip()
                current_key = '副标题'
                current_content = [line.replace('副标题：', '').strip()]
            elif line.startswith('内页文字内容：'):
                if current_key and current_content:
                    result[current_key] = '\n'.join(current_content).strip()
                current_key = '内页文字内容'
                current_content = [line.replace('内页文字内容：', '').strip()]
            elif current_key and line:
                current_content.append(line)
        
        # 处理最后一个部分
        if current_key and current_content:
            result[current_key] = '\n'.join(current_content).strip()
        
        return result
    
    def _generate_simple(self, title: str, content: str) -> Dict[str, str]:
        """简化版本生成内容（不使用API）"""
        # 提取标题关键词
        if '|' in title:
            keywords = title.split('|')[1].strip()
            if '：' in keywords:
                main_topic = keywords.split('：')[0].strip()
                question = keywords.split('：')[1].strip() if '：' in keywords else ""
            else:
                main_topic = keywords
                question = ""
        else:
            main_topic = title
            question = ""

        # 生成多样化的封面主标题
        title_templates = [
            f"🧠 {main_topic}背后的心理秘密",
            f"💡 关于{main_topic}，你不知道的事",
            f"✨ {main_topic}的心理学解读",
            f"🔍 {question}" if question else f"🔍 {main_topic}的真相",
            f"💭 {main_topic}：心理学这样说"
        ]

        import random
        main_title = random.choice(title_templates)

        # 生成多样化的副标题
        subtitle_templates = [
            f"掌握这些心理学知识，让生活更美好 ✨",
            f"原来{main_topic}还有这些门道！",
            f"心理学角度带你重新认识{main_topic}",
            f"这些心理学知识，早知道就好了",
            f"用心理学的眼光看{main_topic}"
        ]

        subtitle = random.choice(subtitle_templates)

        # 生成更丰富的内页文字内容
        content_preview = content[:350] if len(content) > 350 else content

        # 添加生活化的开头
        openings = [
            f"最近在学习{main_topic}相关的心理学知识，发现了一些很有趣的点，想和大家分享一下～",
            f"今天想和大家聊聊{main_topic}这个话题，从心理学的角度来看，真的很有意思！",
            f"关于{main_topic}，心理学有一些很实用的见解，分享给大家～",
            f"之前一直好奇{main_topic}的心理机制，最近学到了一些知识，记录一下！"
        ]

        opening = random.choice(openings)

        # 添加互动结尾
        endings = [
            "你们对这个话题有什么看法呢？欢迎在评论区分享你的想法 💭",
            "这些知识点你们觉得有用吗？有什么想了解的可以留言告诉我～",
            "大家还想了解哪些心理学知识？评论区告诉我吧！",
            "如果这篇内容对你有帮助，记得点赞收藏哦 ❤️"
        ]

        ending = random.choice(endings)

        page_content = f"""{opening}

{content_preview}

这些心理学知识在我们的日常生活中真的很实用，希望能给大家带来一些启发和帮助！

{ending}

#心理学 #心理学知识 #{main_topic} #自我成长 #生活智慧 #心理健康"""

        return {
            '封面主标题': main_title,
            '副标题': subtitle,
            '内页文字内容': page_content
        }

# 使用示例
if __name__ == "__main__":
    # 创建生成器实例
    generator = XiaohongshuContentGenerator()
    
    # 测试生成内容
    test_title = "001|总论：心理学是干什么的？"
    test_content = "心理学是一门研究人类行为和心理过程的科学..."
    
    result = generator.generate_content(test_title, test_content)
    
    print("生成的小红书内容：")
    print(f"封面主标题：{result['封面主标题']}")
    print(f"副标题：{result['副标题']}")
    print(f"内页文字内容：{result['内页文字内容']}")
