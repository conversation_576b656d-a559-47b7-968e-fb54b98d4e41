#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import pandas as pd
import os
import random
import base64
from pathlib import Path
import time
import argparse

class PromptBasedImageGenerator:
    def __init__(self):
        self.api_url = "https://ark.cn-beijing.volces.com/api/v3/images/generations"
        self.api_key = "0b2f220d-bba5-4650-8725-64757bbc1113"
        self.model = "doubao-seedream-4-0-250828"
        
        # 创建输出目录
        self.output_dir = Path("生成的封面图片")
        self.output_dir.mkdir(exist_ok=True)
        
    def load_prompts(self, prompt_file="提示词.txt"):
        """正确解析提示词文件，每个独立单元包含主题和提示词"""
        prompts = []
        if not os.path.exists(prompt_file):
            print(f"❌ 提示词文件不存在: {prompt_file}")
            return prompts
            
        try:
            with open(prompt_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            i = 0
            while i < len(lines):
                line = lines[i].strip()
                
                # 如果当前行不为空，且下一行是提示词
                if line and i + 1 < len(lines):
                    next_line = lines[i + 1].strip()
                    if next_line.startswith('提示词：'):
                        theme = line
                        prompt = next_line.replace('提示词：', '').strip()
                        
                        prompts.append({
                            'theme': theme,
                            'prompt': prompt
                        })
                        
                        print(f"✅ 加载提示词: {theme}")
                        i += 2  # 跳过主题行和提示词行
                    else:
                        i += 1
                else:
                    i += 1
                        
            print(f"✅ 成功加载 {len(prompts)} 个独立提示词单元")
            return prompts
            
        except Exception as e:
            print(f"❌ 加载提示词文件失败: {e}")
            return prompts
    
    def get_random_reference_image(self, ref_dir="结果数据/封面2"):
        """从参考图片文件夹中随机选择一张图片"""
        ref_path = Path(ref_dir)
        if not ref_path.exists():
            print(f"❌ 参考图片目录不存在: {ref_dir}")
            return None
            
        # 获取所有图片文件
        image_files = list(ref_path.glob("*.png")) + list(ref_path.glob("*.jpg")) + list(ref_path.glob("*.jpeg"))
        
        if not image_files:
            print(f"❌ 参考图片目录中没有找到图片文件: {ref_dir}")
            return None
            
        # 随机选择一张图片
        selected_image = random.choice(image_files)
        print(f"📸 选择参考图片: {selected_image.name}")
        return str(selected_image)
    
    def image_to_base64_url(self, image_path):
        """将本地图片转换为base64 URL格式"""
        try:
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
                base64_data = base64.b64encode(image_data).decode('utf-8')
                
                # 判断图片格式
                if image_path.lower().endswith('.png'):
                    mime_type = 'image/png'
                elif image_path.lower().endswith(('.jpg', '.jpeg')):
                    mime_type = 'image/jpeg'
                else:
                    mime_type = 'image/png'  # 默认
                    
                return f"data:{mime_type};base64,{base64_data}"
                
        except Exception as e:
            print(f"❌ 图片转换失败: {e}")
            return None
    
    def generate_image(self, prompt, reference_image_path=None, title="test"):
        """调用豆包API生成图片"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        # 构建请求数据
        data = {
            "model": self.model,
            "prompt": prompt,
            "response_format": "url",
            "size": "2K",
            "stream": False,
            "watermark": True
        }
        
        # 如果有参考图片，添加到请求中
        if reference_image_path:
            base64_url = self.image_to_base64_url(reference_image_path)
            if base64_url:
                data["image"] = [base64_url]
                print(f"📷 使用参考图片: {Path(reference_image_path).name}")
        
        try:
            print(f"🎨 开始生成图片: {title}")
            print(f"📝 提示词: {prompt[:100]}...")
            
            response = requests.post(self.api_url, headers=headers, json=data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ API调用成功")
                
                # 处理响应数据
                if 'data' in result and len(result['data']) > 0:
                    image_url = result['data'][0].get('url')
                    if image_url:
                        # 下载图片
                        image_filename = f"{title}_{int(time.time())}.png"
                        image_path = self.output_dir / image_filename
                        
                        img_response = requests.get(image_url, timeout=30)
                        if img_response.status_code == 200:
                            with open(image_path, 'wb') as f:
                                f.write(img_response.content)
                            print(f"💾 图片已保存: {image_path}")
                            return str(image_path)
                        else:
                            print(f"❌ 下载图片失败: {img_response.status_code}")
                    else:
                        print(f"❌ 响应中没有图片URL")
                else:
                    print(f"❌ 响应数据格式异常: {result}")
                    
            else:
                print(f"❌ API调用失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 生成图片时出错: {e}")
            
        return None
    
    def generate_images_from_prompts(self, prompts, start_index=0, count=None):
        """根据提示词列表生成图片"""
        if count is None:
            count = len(prompts) - start_index
        
        end_index = min(start_index + count, len(prompts))
        
        print(f"将处理第{start_index+1}到第{end_index}个提示词，共{end_index-start_index}个")
        
        results = []
        success_count = 0
        
        for i in range(start_index, end_index):
            prompt_item = prompts[i]
            theme = prompt_item['theme']
            prompt = prompt_item['prompt']
            
            print(f"\n📚 处理第 {i+1}/{len(prompts)} 个提示词")
            print(f"🎯 主题: {theme}")
            
            # 获取随机参考图片
            reference_image = self.get_random_reference_image()
            
            # 生成安全的文件名
            safe_title = "".join(c for c in theme if c.isalnum() or c in (' ', '-', '_')).strip()[:50]
            
            # 生成图片
            generated_image = self.generate_image(prompt, reference_image, safe_title)
            
            result = {
                'index': i + 1,
                'theme': theme,
                'prompt': prompt,
                'reference_image': reference_image,
                'generated_image': generated_image,
                'success': generated_image is not None
            }
            
            results.append(result)
            
            if result['success']:
                success_count += 1
            
            # 添加延迟避免API限制
            if i < end_index - 1:
                print("⏱️ 等待3秒...")
                time.sleep(3)
        
        return results, success_count

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='基于提示词的豆包图像生成器')
    parser.add_argument('--count', type=int, default=1, help='要处理的提示词数量，默认为1')
    parser.add_argument('--start', type=int, default=0, help='起始提示词索引，默认为0')
    parser.add_argument('--output', type=str, default='提示词图片生成结果.xlsx', help='输出文件名')
    args = parser.parse_args()
    
    print("🎨 基于提示词的豆包图像生成器")
    print("=" * 60)
    
    # 初始化生成器
    generator = PromptBasedImageGenerator()
    
    # 加载提示词
    prompts = generator.load_prompts()
    if not prompts:
        print("❌ 无法加载提示词，退出程序")
        return
    
    print(f"\n📋 提示词列表预览:")
    for i, prompt_item in enumerate(prompts[:5]):  # 显示前5个
        print(f"   {i+1}. {prompt_item['theme']}")
    if len(prompts) > 5:
        print(f"   ... 还有 {len(prompts)-5} 个提示词")
    
    # 生成图片
    try:
        results, success_count = generator.generate_images_from_prompts(
            prompts, args.start, args.count
        )
        
        # 保存结果
        results_df = pd.DataFrame(results)
        results_file = generator.output_dir / args.output
        results_df.to_excel(results_file, index=False)
        
        print(f"\n🎉 图片生成完成!")
        print(f"   - 处理提示词数: {len(results)}")
        print(f"   - 成功生成: {success_count}")
        print(f"   - 失败数量: {len(results) - success_count}")
        print(f"   - 结果文件: {results_file}")
        print(f"   - 图片目录: {generator.output_dir}")
        
        # 显示生成的主题
        print(f"\n📝 已生成的主题:")
        for result in results:
            status = "✅" if result['success'] else "❌"
            print(f"   {status} {result['theme']}")
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")

if __name__ == "__main__":
    main()
