#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from openai import OpenAI

# API配置
API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
client = OpenAI(api_key=API_KEY, base_url=BASE_URL)

def test_api_connection():
    """测试API连接"""
    print("🔍 测试API连接...")
    
    try:
        # 简单的测试请求
        response = client.chat.completions.create(
            model="Doubao-Seed-1.6-thinking-250615",
            messages=[
                {"role": "user", "content": "你好，请回复'API连接成功'"}
            ],
            max_tokens=50
        )
        
        result = response.choices[0].message.content
        print(f"✅ API连接成功！响应: {result}")
        return True
        
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def test_xiaohongshu_generation():
    """测试小红书内容生成"""
    print("\n🎨 测试小红书内容生成...")
    
    # 测试数据
    test_title = "001|总论：心理学是干什么的？"
    test_content = """
    我是李松蔚。这是我们《心理学通识》的第一节课，这趟长达一年的旅程，从哪里开始呢？先从我对心理学这门学科的认识讲起吧。

    你可能觉得，心理学应该是人类最古老的一门学科了吧？因为人总是对自己的内心世界充满好奇。但事实上，心理学很年轻，它作为一门科学的学科，诞生的标志是在1879年。冯特在莱比锡大学创建了世界上第一个心理学实验室，距离今天不过一百多年。

    那么，心理学上升为一门学科，它要完成什么任务呢？几乎所有心理学教科书，一开头都会告诉你：作为一门学科，心理学的目标在于更好地描述、解释、预测和干预人类行为。
    """
    
    # 系统提示词
    system_prompt = (
        "我是一个小红书心理学图文博主，专门将心理学知识转化为适合小红书平台的优质内容。"
        "我擅长提取核心知识点，去除个人化表述，形成结构化的知识讲解，并增加对读者的启发。"
    )
    
    # 用户提示词
    user_prompt = f"""
    我是一个小红书心理学图文博主，请基于以下内容做出下处理：

    课程标题：{test_title}
    课程内容：{test_content}

    处理要求：
    1、提取核心知识点，去掉讲述人口吻和老师个人信息
    2、形成结构化小红书知识讲解内容并增加对人的启发
    3、生成符合小红书表达的格式和符号等
    4、总结封面主标题和副标题

    具体要求：
    - 封面主标题（15-25字）：吸引眼球，使用疑问句或感叹句，包含emoji
    - 副标题（20-35字）：补充说明或引发思考，语气亲切自然
    - 内页文字内容（480-520字）：
      * 开头要有吸引人的hook
      * 用生活化例子解释心理学概念
      * 分段清晰，每段2-3句话
      * 包含2-3个实用知识点
      * 语言口语化，像和朋友聊天
      * 适当使用emoji增加趣味性
      * 结尾有行动建议或互动问题
      * 加上相关话题标签

    请按以下格式输出：
    封面主标题：xxx
    副标题：xxx
    内页文字：
    xxx
    """
    
    try:
        # 调用API
        response = client.chat.completions.create(
            model="Doubao-Seed-1.6-thinking-250615",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ],
            temperature=0.7,
            max_tokens=1500
        )
        
        result = response.choices[0].message.content
        print("✅ 小红书内容生成成功！")
        print("\n" + "="*60)
        print("生成的内容：")
        print("="*60)
        print(result)
        print("="*60)
        
        # 解析结果
        lines = result.split('\n')
        main_title = ''
        subtitle = ''
        content_text = ''
        current_section = None
        
        for line in lines:
            if line.startswith('封面主标题：'):
                main_title = line.replace('封面主标题：', '').strip()
            elif line.startswith('副标题：'):
                subtitle = line.replace('副标题：', '').strip()
            elif line.startswith('内页文字：') or line.startswith('内页文字内容：'):
                current_section = 'content'
            elif current_section == 'content' and line.strip():
                content_text += line + '\n'
        
        print(f"\n📊 解析结果：")
        print(f"封面主标题长度: {len(main_title)} 字符")
        print(f"副标题长度: {len(subtitle)} 字符")
        print(f"内页内容长度: {len(content_text.strip())} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 小红书内容生成失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始API测试")
    print("="*60)
    
    # 测试API连接
    if not test_api_connection():
        print("❌ API连接失败，请检查API配置")
        return
    
    # 测试小红书内容生成
    if not test_xiaohongshu_generation():
        print("❌ 小红书内容生成失败")
        return
    
    print("\n🎉 所有测试通过！API配置正确，可以正常使用。")
    print("\n💡 使用建议：")
    print("1. 运行 python ai_model.py --count 1 --start 0 测试单条记录")
    print("2. 运行 python ai_model.py --count 5 --start 0 批量处理5条记录")
    print("3. 运行 python ai_model.py --count 10 --start 5 处理第6-15条记录")

if __name__ == "__main__":
    main()
