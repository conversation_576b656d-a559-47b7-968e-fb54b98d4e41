import pandas as pd
import os
import time
import json
import requests

# 定义文件路径
excel_input = '心理学课程内容.xlsx'
excel_output = '心理学小红书内容.xlsx'

# 这里需要设置你的OpenAI API密钥
# 实际使用时请替换为你的API密钥
API_KEY = "your_openai_api_key_here"

# 使用OpenAI API生成小红书风格内容
def generate_xiaohongshu_content_with_api(title, content):
    # 提取标题关键词
    if '|' in title:
        keywords = title.split('|')[1].strip()
    else:
        keywords = title
    
    # 简化内容（取前1000个字符）
    simplified_content = content[:1000] + "..."
    
    # 构建提示词
    prompt = f"""请根据以下心理学课程内容，生成一篇适合小红书平台的心理学科普文章。
    
    课程标题：{title}
    课程内容摘要：{simplified_content}
    
    要求：
    1. 生成一个吸引人的封面主标题（不超过15字）
    2. 生成一个有吸引力的副标题（不超过20字）
    3. 生成正文内容（约500字），通俗易懂，有实用性，适合小红书平台
    4. 内容要有科普性质，同时保持轻松活泼的风格
    5. 可以适当加入emoji表情
    6. 确保内容结构清晰，易于阅读
    
    请按以下格式返回：
    封面主标题：xxx
    副标题：xxx
    内页文字内容：xxx
    """
    
    try:
        # 调用OpenAI API
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {API_KEY}"
        }
        
        data = {
            "model": "gpt-3.5-turbo",
            "messages": [
                {"role": "system", "content": "你是一位专业的心理学科普作家，擅长创作适合小红书平台的内容。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.7
        }
        
        response = requests.post(
            "https://api.openai.com/v1/chat/completions",
            headers=headers,
            data=json.dumps(data)
        )
        
        if response.status_code == 200:
            response_data = response.json()
            content = response_data['choices'][0]['message']['content']
            
            # 解析返回的内容
            main_title = ""
            subtitle = ""
            page_content = ""
            
            lines = content.split('\n')
            for line in lines:
                if line.startswith("封面主标题："):
                    main_title = line.replace("封面主标题：", "").strip()
                elif line.startswith("副标题："):
                    subtitle = line.replace("副标题：", "").strip()
                elif line.startswith("内页文字内容："):
                    page_content = line.replace("内页文字内容：", "").strip()
                    # 获取后续所有行作为内容
                    content_index = lines.index(line)
                    if content_index < len(lines) - 1:
                        page_content += "\n" + "\n".join(lines[content_index+1:])
            
            return {
                '封面主标题': main_title,
                '副标题': subtitle,
                '内页文字内容': page_content
            }
        else:
            print(f"API调用失败: {response.status_code} - {response.text}")
            # 返回默认内容
            return generate_default_content(keywords, simplified_content)
    except Exception as e:
        print(f"生成内容时出错: {e}")
        # 返回默认内容
        return generate_default_content(keywords, simplified_content)

# 生成默认内容（当API调用失败时使用）
def generate_default_content(keywords, simplified_content):
    main_title = f"💡 {keywords}的心理奥秘"
    subtitle = f"掌握这些心理学知识，让生活更美好"
    page_content = f"今天和大家分享关于{keywords}的心理学知识。\n\n{simplified_content[:300]}...\n\n这些知识对我们的日常生活有很大帮助，希望对你有所启发！❤️"
    
    return {
        '封面主标题': main_title,
        '副标题': subtitle,
        '内页文字内容': page_content
    }

# 主函数
def main():
    print(f"开始处理Excel文件: {excel_input}")
    
    # 读取Excel文件
    df = pd.read_excel(excel_input)
    print(f"成功读取{len(df)}个章节")
    
    # 为每个章节生成小红书内容
    xiaohongshu_contents = []
    for i, row in df.iterrows():
        print(f"正在处理第{i+1}个章节: {row['课程标题']}")
        
        # 检查是否已有小红书内容
        if '封面主标题' in df.columns and pd.notna(row['封面主标题']):
            print(f"  已有内容，跳过生成")
            xhs_content = {
                '封面主标题': row['封面主标题'],
                '副标题': row['副标题'],
                '内页文字内容': row['内页文字内容']
            }
        else:
            # 调用API生成内容
            xhs_content = generate_xiaohongshu_content_with_api(row['课程标题'], row['课程文章内容'])
            # 避免API限流
            time.sleep(1)
        
        xiaohongshu_contents.append(xhs_content)
    
    # 创建小红书内容数据框
    df_xhs = pd.DataFrame(xiaohongshu_contents)
    
    # 如果原数据框中没有这些列，则添加
    for col in df_xhs.columns:
        if col not in df.columns:
            df[col] = df_xhs[col].values
    
    # 保存到Excel
    df.to_excel(excel_output, index=False)
    print(f"Excel文件已保存: {excel_output}")

if __name__ == "__main__":
    main()