# 心理学PDF转小红书内容生成器

## 项目概述

本项目可以将PDF格式的心理学课程内容自动转换为适合小红书平台发布的图文内容，包括封面主标题、副标题和内页文字内容。

## 功能特点

✅ **PDF内容解析**: 自动识别和分割PDF中的课程章节  
✅ **智能内容生成**: 支持大模型API和简化版本两种生成方式  
✅ **小红书风格优化**: 生成符合小红书用户喜好的内容格式  
✅ **Excel格式化输出**: 提供美观的Excel表格输出  
✅ **内容质量验证**: 自动检查生成内容的质量和完整性  

## 文件结构

```
心理学小红书/
├── 李松蔚-心理学通识.pdf          # 原始PDF文件
├── process_pdf.py                  # 主处理脚本
├── xiaohongshu_generator.py        # 小红书内容生成器
├── excel_formatter.py              # Excel格式化工具
├── validate_results.py             # 内容验证工具
├── 心理学课程内容.csv              # CSV格式输出
├── 心理学课程内容_格式化.xlsx      # 格式化的Excel输出
└── 使用说明.md                     # 本文件
```

## 使用方法

### 1. 基础使用（简化版本）

直接运行主脚本：
```bash
python process_pdf.py
```

这将使用内置的简化版本生成器，无需API密钥。

### 2. 高级使用（大模型API）

如果您有OpenAI API密钥，可以获得更高质量的内容：

1. 设置环境变量：
```bash
set OPENAI_API_KEY=your_api_key_here
```

2. 运行脚本：
```bash
python process_pdf.py
```

### 3. 格式化Excel输出

生成格式化的Excel文件：
```bash
python excel_formatter.py
```

### 4. 验证内容质量

检查生成内容的质量：
```bash
python validate_results.py
```

## 输出内容说明

生成的Excel表格包含以下列：

| 列名 | 说明 | 示例 |
|------|------|------|
| 课程标题 | 原始PDF中的课程标题 | 001\|总论：心理学是干什么的？ |
| 课程文章内容 | 从PDF提取的完整课程内容 | 我是李松蔚。这是我们《心理学通识》的第一节课... |
| 封面主标题 | 适合小红书的吸引人标题 | 💡 心理学是干什么的？背后的秘密 |
| 副标题 | 补充说明或引发思考 | 掌握这些心理学知识，让生活更美好 ✨ |
| 内页文字内容 | 500字左右的小红书正文 | 今天和大家分享关于心理学的知识... |

## 小红书内容特点

生成的内容具有以下特点：

- **封面主标题**: 15-25字，包含emoji，吸引眼球
- **副标题**: 20-35字，补充说明或提出问题
- **内页内容**: 480-520字，语言通俗易懂，包含：
  - 生活化的开头
  - 清晰的段落结构
  - 实用的心理学知识点
  - 互动性的结尾
  - 相关话题标签

## 处理结果

本次处理结果：
- ✅ 成功解析PDF文件（130页，135,291字符）
- ✅ 识别并分割40个课程章节
- ✅ 生成40套完整的小红书内容
- ✅ 所有内容包含emoji和话题标签
- ✅ 内容长度符合小红书平台要求

## 依赖包

```
PyPDF2==3.0.1
pandas==2.3.1
openpyxl==3.1.5
openai  # 可选，仅在使用API时需要
```

## 注意事项

1. **API使用**: 如果使用OpenAI API，请注意API调用限制和费用
2. **文件权限**: 确保Excel文件未被其他程序占用
3. **内容审核**: 生成的内容建议人工审核后再发布
4. **版权声明**: 请确保遵守原始内容的版权要求

## 故障排除

### 常见问题

**Q: Excel文件保存失败？**  
A: 检查文件是否被其他程序打开，或使用CSV格式作为备选

**Q: API调用失败？**  
A: 检查API密钥设置，或使用简化版本生成器

**Q: PDF解析不完整？**  
A: 确认PDF文件格式正确，文本可以正常提取

## 联系支持

如有问题或建议，请检查生成的日志信息或运行验证脚本进行诊断。
