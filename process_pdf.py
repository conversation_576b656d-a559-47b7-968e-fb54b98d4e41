import PyPDF2
import pandas as pd
import re
import os
import unicodedata
from xiaohongshu_generator import XiaohongshuContentGenerator

# 定义文件路径
pdf_path = '李松蔚-心理学通识.pdf'
excel_output = '心理学课程内容.xlsx'

# 清理文本中的非法字符
def clean_text(text):
    # 移除控制字符
    text = ''.join(ch for ch in text if unicodedata.category(ch)[0] != 'C')
    # 替换Excel不支持的字符
    text = text.replace('\u000B', ' ').replace('\u000C', ' ')
    # 替换其他可能导致问题的字符
    text = text.replace('\r', ' ').replace('\t', ' ')
    return text

# 读取PDF文件
def extract_text_from_pdf(pdf_path):
    with open(pdf_path, 'rb') as file:
        pdf_reader = PyPDF2.PdfReader(file)
        text = ''
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            text += page.extract_text()
        return clean_text(text)

# 按课程拆分内容
def split_by_chapters(text):
    # 使用正则表达式查找课程标题和内容
    # 根据PDF分析，课程标题格式为：数字编号|标题：副标题？
    pattern = r'(\d{3}\|[^？！。]*[？！。]?)'

    # 找到所有课程标题的位置
    matches = []
    for match in re.finditer(pattern, text):
        matches.append({
            'title': match.group(1).strip(),
            'start': match.start(),
            'end': match.end()
        })

    print(f"找到 {len(matches)} 个课程标题")

    result = []
    for i, match in enumerate(matches):
        title = match['title']

        # 确定内容的开始和结束位置
        content_start = match['end']
        if i < len(matches) - 1:
            # 不是最后一个课程，内容到下一个课程标题开始
            content_end = matches[i + 1]['start']
        else:
            # 最后一个课程，内容到文本结束
            content_end = len(text)

        # 提取内容
        content = text[content_start:content_end].strip()

        # 清理内容，移除多余的空白字符
        content = re.sub(r'\n\s*\n', '\n\n', content)  # 规范化段落间距
        content = re.sub(r'[ \t]+', ' ', content)  # 规范化空格

        # 清理标题和内容
        title = clean_text(title)
        content = clean_text(content)

        result.append({
            '课程标题': title,
            '课程文章内容': content
        })

        print(f"处理课程 {i+1}: {title[:50]}...")

    return result

# 生成小红书风格内容（使用新的生成器）
def generate_xiaohongshu_content(title, content, generator):
    """使用XiaohongshuContentGenerator生成内容"""
    result = generator.generate_content(title, content)

    # 清理生成的内容
    return {
        '封面主标题': clean_text(result['封面主标题']),
        '副标题': clean_text(result['副标题']),
        '内页文字内容': clean_text(result['内页文字内容'])
    }

# 主函数
def main():
    print(f"开始处理PDF文件: {pdf_path}")

    # 初始化小红书内容生成器
    print("初始化小红书内容生成器...")
    generator = XiaohongshuContentGenerator()

    # 提取PDF文本
    text = extract_text_from_pdf(pdf_path)
    print(f"PDF文本提取完成，共{len(text)}字符")

    # 按章节拆分
    chapters = split_by_chapters(text)
    print(f"成功拆分出{len(chapters)}个章节")

    # 创建数据框
    df = pd.DataFrame(chapters)

    # 为每个章节生成小红书内容
    xiaohongshu_contents = []
    for i, row in df.iterrows():
        print(f"正在处理第{i+1}个章节: {row['课程标题']}")
        xhs_content = generate_xiaohongshu_content(row['课程标题'], row['课程文章内容'], generator)
        xiaohongshu_contents.append(xhs_content)

        # 添加延迟以避免API限制
        if generator.use_api and i < len(df) - 1:
            print("等待1秒以避免API限制...")
            import time
            time.sleep(1)

    # 创建小红书内容数据框
    df_xhs = pd.DataFrame(xiaohongshu_contents)

    # 合并数据框
    result_df = pd.concat([df, df_xhs], axis=1)

    # 尝试保存到Excel，如果失败则保存为CSV
    try:
        result_df.to_excel(excel_output, index=False)
        print(f"Excel文件已保存: {excel_output}")
    except Exception as e:
        print(f"保存Excel失败: {e}")
        csv_output = excel_output.replace('.xlsx', '.csv')
        result_df.to_csv(csv_output, index=False, encoding='utf-8-sig')
        print(f"已保存为CSV文件: {csv_output}")

if __name__ == "__main__":
    main()
