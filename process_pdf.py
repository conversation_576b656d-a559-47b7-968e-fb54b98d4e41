import PyPDF2
import pandas as pd
import re
import os

# 定义文件路径
pdf_path = '李松蔚-心理学通识.pdf'
excel_output = '心理学课程内容.xlsx'

# 读取PDF文件
def extract_text_from_pdf(pdf_path):
    with open(pdf_path, 'rb') as file:
        pdf_reader = PyPDF2.PdfReader(file)
        text = ''
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            text += page.extract_text()
        return text

# 按课程拆分内容
def split_by_lessons(text):
    # 使用正则表达式查找课程标题和内容
    # 假设课程标题格式为：第X课 标题
    pattern = r'(第\d+课[^\n]*)(\n|.)*?(?=第\d+课|$)'
    lessons = re.findall(pattern, text, re.DOTALL)
    
    result = []
    for title, content in lessons:
        # 清理标题和内容
        title = title.strip()
        content = content.strip()
        result.append({'课程标题': title, '课程文章内容': content})
    
    return result

# 生成小红书风格内容
def generate_xiaohongshu_content(title, content):
    # 这里简化处理，实际项目中可以调用大模型API
    # 提取标题关键词
    keywords = title.split(' ')[-1] if ' ' in title else title
    
    # 生成封面主标题
    main_title = f"心理学干货：{keywords}完全指南"
    
    # 生成副标题
    subtitle = f"掌握{keywords}的核心知识，提升心理健康"
    
    # 生成内页文字内容（简化版，实际应调用大模型）
    # 提取内容的前500个字符作为示例
    page_content = content[:500] + "..."
    
    return {
        '封面主标题': main_title,
        '副标题': subtitle,
        '内页文字内容': page_content
    }

# 主函数
def main():
    print(f"开始处理PDF文件: {pdf_path}")
    
    # 提取PDF文本
    text = extract_text_from_pdf(pdf_path)
    print(f"PDF文本提取完成，共{len(text)}字符")
    
    # 按课程拆分
    lessons = split_by_lessons(text)
    print(f"成功拆分出{len(lessons)}节课程")
    
    # 创建数据框
    df = pd.DataFrame(lessons)
    
    # 为每节课生成小红书内容
    xiaohongshu_contents = []
    for _, row in df.iterrows():
        xhs_content = generate_xiaohongshu_content(row['课程标题'], row['课程文章内容'])
        xiaohongshu_contents.append(xhs_content)
    
    # 创建小红书内容数据框
    df_xhs = pd.DataFrame(xiaohongshu_contents)
    
    # 合并数据框
    result_df = pd.concat([df, df_xhs], axis=1)
    
    # 保存到Excel
    result_df.to_excel(excel_output, index=False)
    print(f"Excel文件已保存: {excel_output}")

if __name__ == "__main__":
    main()