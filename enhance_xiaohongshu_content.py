import pandas as pd
import os
import re

# 定义文件路径
excel_input = '心理学课程内容.xlsx'
excel_output = '心理学小红书内容_增强版.xlsx'

# 生成更好的小红书风格内容
def enhance_xiaohongshu_content(title, content):
    # 提取标题关键词
    if '|' in title:
        keywords = title.split('|')[1].strip()
    else:
        keywords = title
    
    # 简化内容（取前1000个字符）
    simplified_content = content[:1000]
    
    # 提取关键概念（简单实现，实际应用中可以使用NLP技术）
    concepts = re.findall(r'[\u4e00-\u9fa5]{2,6}', simplified_content)
    concepts = list(set([c for c in concepts if len(c) >= 2]))
    key_concepts = concepts[:5]  # 取前5个概念
    
    # 生成更吸引人的封面主标题
    main_titles = [
        f"💡 {keywords}：你不知道的心理秘密",
        f"⚡ 掌握{keywords}，改变你的生活",
        f"🔍 {keywords}背后的心理真相",
        f"✨ {keywords}：心理学家都在用的方法",
        f"🧠 {keywords}：心理学视角解析"
    ]
    main_title = main_titles[hash(title) % len(main_titles)]
    
    # 生成更有吸引力的副标题
    subtitles = [
        f"这些心理学知识让你秒懂{keywords}",
        f"掌握这些技巧，轻松应对{keywords}",
        f"心理学家不会告诉你的{keywords}秘密",
        f"改变思维方式，重新认识{keywords}",
        f"学会这些方法，让{keywords}不再困扰你"
    ]
    subtitle = subtitles[hash(title) % len(subtitles)]
    
    # 生成更丰富的内容结构
    intro = f"今天和大家分享关于{keywords}的心理学知识，这些内容可能会颠覆你的认知！🧠\n\n"
    
    # 生成内容主体
    body = f"在心理学中，{keywords}是一个非常重要的概念。{simplified_content[:200]}...\n\n"
    
    # 生成关键点
    key_points = "📌 核心要点：\n"
    for i, concept in enumerate(key_concepts):
        key_points += f"{i+1}. {concept}与{keywords}的关系\n"
    
    # 生成应用建议
    applications = f"\n💪 实用建议：\n1. 在日常生活中注意观察{keywords}的表现\n2. 学会用心理学知识分析{keywords}背后的原因\n3. 尝试用新的方式理解{keywords}，改变固有思维\n"
    
    # 生成结语
    conclusion = f"\n希望今天分享的{keywords}心理学知识对你有所启发！如果你有任何问题，欢迎在评论区留言讨论~ ❤️"
    
    # 组合完整内容
    page_content = intro + body + key_points + applications + conclusion
    
    return {
        '封面主标题': main_title,
        '副标题': subtitle,
        '内页文字内容': page_content
    }

# 主函数
def main():
    print(f"开始处理Excel文件: {excel_input}")
    
    # 读取Excel文件
    df = pd.read_excel(excel_input)
    print(f"成功读取{len(df)}个章节")
    
    # 为每个章节生成增强版小红书内容
    enhanced_contents = []
    for i, row in df.iterrows():
        print(f"正在处理第{i+1}个章节: {row['课程标题']}")
        enhanced_content = enhance_xiaohongshu_content(row['课程标题'], row['课程文章内容'])
        enhanced_contents.append(enhanced_content)
    
    # 创建增强版小红书内容数据框
    df_enhanced = pd.DataFrame(enhanced_contents)
    
    # 合并数据框
    result_df = pd.concat([df, df_enhanced], axis=1)
    
    # 保存到Excel
    result_df.to_excel(excel_output, index=False)
    print(f"Excel文件已保存: {excel_output}")

if __name__ == "__main__":
    main()