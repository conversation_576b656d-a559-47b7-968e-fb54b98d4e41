import PyPDF2
import os

# 检查PDF文件是否存在
pdf_path = '李松蔚-心理学通识.pdf'
if not os.path.exists(pdf_path):
    print(f"PDF文件不存在: {pdf_path}")
    exit(1)

print(f"PDF文件存在: {pdf_path}")
print(f"文件大小: {os.path.getsize(pdf_path)} bytes")

try:
    # 尝试读取PDF
    with open(pdf_path, 'rb') as file:
        pdf_reader = PyPDF2.PdfReader(file)
        print(f"PDF页数: {len(pdf_reader.pages)}")
        
        # 读取所有页面内容，寻找课程标题模式
        all_text = ""
        for i in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[i]
            text = page.extract_text()
            all_text += text + "\n"

        # 寻找课程标题模式
        import re

        # 寻找类似 "001|总论：心理学是干什么的？" 的模式
        pattern1 = r'(\d{3}\|[^？！。]*[？！。]?)'
        matches1 = re.findall(pattern1, all_text)

        print(f"\n找到的课程标题模式1 (共{len(matches1)}个):")
        for i, match in enumerate(matches1[:10]):  # 只显示前10个
            print(f"{i+1}. {match}")

        # 寻找其他可能的课程标题模式
        pattern2 = r'(第\d+课[^？！。\n]*)'
        matches2 = re.findall(pattern2, all_text)

        print(f"\n找到的课程标题模式2 (共{len(matches2)}个):")
        for i, match in enumerate(matches2[:10]):  # 只显示前10个
            print(f"{i+1}. {match}")

        # 显示文本的前2000字符来了解整体结构
        print(f"\n=== PDF整体内容结构 (前2000字符) ===")
        print(all_text[:2000])
        print("=" * 50)
            
except Exception as e:
    print(f"读取PDF时出错: {e}")
