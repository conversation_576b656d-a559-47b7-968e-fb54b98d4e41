# 心理学小红书内容生成器 - AI增强版使用指南

## 🚀 更新内容

### 新的提示词策略
根据您的要求，我们已经更新了AI模型的提示词，现在的处理方式更加专业：

1. **提取核心知识点** - 去掉讲述人口吻和老师个人信息
2. **结构化知识讲解** - 形成清晰的小红书知识内容并增加启发性
3. **小红书格式优化** - 生成符合小红书表达的格式和符号
4. **标题优化** - 总结吸引人的封面主标题和副标题

### API配置
```python
API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
```

## 📋 使用方法

### 1. 测试API连接
```bash
python test_api.py
```
这将测试API连接并展示生成效果。

### 2. 处理单条记录（测试）
```bash
python ai_model.py --count 1 --start 0 --output 测试结果.xlsx
```

### 3. 批量处理记录
```bash
# 处理前5条记录
python ai_model.py --count 5 --start 0 --output 心理学小红书内容_1-5.xlsx

# 处理第6-15条记录
python ai_model.py --count 10 --start 5 --output 心理学小红书内容_6-15.xlsx

# 处理所有40条记录
python ai_model.py --count 40 --start 0 --output 心理学小红书内容_完整版.xlsx
```

## 🎯 新提示词特点

### 系统提示词
```
我是一个小红书心理学图文博主，专门将心理学知识转化为适合小红书平台的优质内容。
我擅长提取核心知识点，去除个人化表述，形成结构化的知识讲解，并增加对读者的启发。
```

### 处理要求
1. **提取核心知识点** - 去掉讲述人口吻和老师个人信息
2. **结构化讲解** - 形成小红书知识讲解内容并增加启发
3. **格式优化** - 生成符合小红书表达的格式和符号
4. **标题总结** - 封面主标题和副标题

### 内容要求
- **封面主标题**（15-25字）：吸引眼球，使用疑问句或感叹句，包含emoji
- **副标题**（20-35字）：补充说明或引发思考，语气亲切自然
- **内页文字内容**（480-520字）：
  * 开头要有吸引人的hook
  * 用生活化例子解释心理学概念
  * 分段清晰，每段2-3句话
  * 包含2-3个实用知识点
  * 语言口语化，像和朋友聊天
  * 适当使用emoji增加趣味性
  * 结尾有行动建议或互动问题
  * 加上相关话题标签

## 📊 生成效果示例

### 测试结果展示
**原始标题**: 001|总论：心理学是干什么的？

**AI生成结果**:
- **封面主标题**: 心理学到底能帮我们什么？🤔你想知道的答案在这里！
- **副标题**: 心理学不仅仅是"看透人心"，它还能带来哪些真实的改变？一起来聊聊吧～
- **内容特点**: 
  - 去除了"我是李松蔚"等个人信息
  - 用生活化例子解释概念
  - 包含实用知识点
  - 语言口语化，有互动性
  - 包含相关话题标签

## 🔧 文件说明

### 核心文件
- `ai_model.py` - 主处理脚本（已更新提示词）
- `xiaohongshu_generator.py` - 内容生成器（已更新提示词）
- `test_api.py` - API测试脚本

### 输出文件
- `心理学小红书内容_AI增强版.xlsx` - AI生成的增强版内容
- `心理学课程内容.xlsx` - 原始处理结果
- `心理学课程内容_格式化.xlsx` - 格式化版本

## ⚡ 性能优化

### 批量处理建议
1. **小批量测试**: 先处理1-5条记录验证效果
2. **分批处理**: 每次处理10-20条记录，避免API限制
3. **错误恢复**: 脚本支持自动保存，中途出错不会丢失数据
4. **备用方案**: API失败时自动使用本地生成器

### API调用优化
- 每次调用间隔2秒，避免频率限制
- 自动错误重试机制
- 支持断点续传

## 🎨 内容质量对比

### 简化版本 vs AI增强版

| 特征 | 简化版本 | AI增强版 |
|------|----------|----------|
| 个人化表述 | 保留 | 已去除 |
| 知识点提取 | 基础 | 精准提取 |
| 生活化例子 | 简单 | 丰富生动 |
| 互动性 | 一般 | 强互动 |
| 小红书风格 | 基础 | 专业优化 |

## 📈 使用建议

### 推荐工作流程
1. **API测试**: 运行 `python test_api.py` 确认连接
2. **单条测试**: 处理1条记录检查效果
3. **小批量处理**: 处理5-10条记录
4. **全量处理**: 根据需要处理所有记录
5. **质量检查**: 使用 `python validate_results.py` 验证

### 注意事项
- API有调用频率限制，建议分批处理
- 生成的内容建议人工审核后发布
- 保持原始数据备份
- 定期检查API余额

## 🎉 总结

AI增强版现在完全符合您的要求：
✅ 提取核心知识点，去掉个人化表述  
✅ 形成结构化小红书知识讲解  
✅ 生成符合小红书的格式和符号  
✅ 优化封面主标题和副标题  
✅ 增加对读者的启发性内容  

现在您可以开始使用AI增强版来批量生成高质量的小红书心理学内容了！
