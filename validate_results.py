import pandas as pd
import os
import re

def validate_excel_content(file_path):
    """验证Excel文件内容的质量"""
    print(f"正在验证文件: {file_path}")
    print("=" * 60)
    
    try:
        # 读取文件
        if file_path.endswith('.xlsx'):
            df = pd.read_excel(file_path)
        else:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
        
        print(f"✅ 文件读取成功")
        print(f"📊 数据概览:")
        print(f"   - 总行数: {len(df)}")
        print(f"   - 总列数: {len(df.columns)}")
        print(f"   - 列名: {list(df.columns)}")
        
        # 验证必需的列是否存在
        required_columns = ['课程标题', '课程文章内容', '封面主标题', '副标题', '内页文字内容']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"❌ 缺少必需的列: {missing_columns}")
        else:
            print(f"✅ 所有必需的列都存在")
        
        # 检查数据完整性
        print(f"\n📋 数据完整性检查:")
        for col in df.columns:
            null_count = df[col].isnull().sum()
            empty_count = (df[col] == '').sum()
            print(f"   - {col}: {null_count} 个空值, {empty_count} 个空字符串")
        
        # 验证内容质量
        print(f"\n🔍 内容质量分析:")
        
        # 检查课程标题格式
        title_pattern = r'\d{3}\|.*'
        valid_titles = df['课程标题'].str.match(title_pattern).sum()
        print(f"   - 课程标题格式正确: {valid_titles}/{len(df)}")
        
        # 检查内容长度
        content_lengths = df['课程文章内容'].str.len()
        print(f"   - 课程内容平均长度: {content_lengths.mean():.0f} 字符")
        print(f"   - 课程内容长度范围: {content_lengths.min()}-{content_lengths.max()} 字符")
        
        # 检查小红书内容长度
        title_lengths = df['封面主标题'].str.len()
        subtitle_lengths = df['副标题'].str.len()
        page_content_lengths = df['内页文字内容'].str.len()
        
        print(f"   - 封面主标题平均长度: {title_lengths.mean():.0f} 字符 (建议15-25字)")
        print(f"   - 副标题平均长度: {subtitle_lengths.mean():.0f} 字符 (建议20-35字)")
        print(f"   - 内页内容平均长度: {page_content_lengths.mean():.0f} 字符 (建议480-520字)")
        
        # 显示几个示例
        print(f"\n📝 内容示例:")
        for i in range(min(3, len(df))):
            print(f"\n--- 示例 {i+1} ---")
            print(f"课程标题: {df.iloc[i]['课程标题']}")
            print(f"封面主标题: {df.iloc[i]['封面主标题']}")
            print(f"副标题: {df.iloc[i]['副标题']}")
            print(f"内页内容 (前100字): {df.iloc[i]['内页文字内容'][:100]}...")
        
        # 检查是否包含emoji
        emoji_pattern = r'[😀-🙏🌀-🗿🚀-🛿🇀-🇿✀-➿⚀-⚿]'
        titles_with_emoji = df['封面主标题'].str.contains(emoji_pattern, na=False).sum()
        content_with_emoji = df['内页文字内容'].str.contains(emoji_pattern, na=False).sum()
        
        print(f"\n🎨 小红书风格特征:")
        print(f"   - 封面标题包含emoji: {titles_with_emoji}/{len(df)}")
        print(f"   - 内页内容包含emoji: {content_with_emoji}/{len(df)}")
        
        # 检查是否包含话题标签
        hashtag_pattern = r'#\w+'
        content_with_hashtags = df['内页文字内容'].str.contains(hashtag_pattern, na=False).sum()
        print(f"   - 内页内容包含话题标签: {content_with_hashtags}/{len(df)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")
        return False

def main():
    """主验证函数"""
    print("🔍 心理学课程内容质量验证")
    print("=" * 60)
    
    # 检查文件是否存在
    files_to_check = [
        '心理学课程内容_格式化.xlsx',
        '心理学课程内容.csv'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"\n检查文件: {file_path}")
            validate_excel_content(file_path)
            print("\n" + "=" * 60)
        else:
            print(f"⚠️  文件不存在: {file_path}")
    
    # 总结
    print(f"\n✅ 验证完成！")
    print(f"📁 生成的文件:")
    for file in os.listdir('.'):
        if file.endswith(('.xlsx', '.csv')) and '心理学' in file:
            size = os.path.getsize(file) / 1024  # KB
            print(f"   - {file} ({size:.1f} KB)")

if __name__ == "__main__":
    main()
