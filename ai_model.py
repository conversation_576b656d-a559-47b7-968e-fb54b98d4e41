import pandas as pd
import time
import argparse
from xiaohongshu_generator import XiaohongshuContentGenerator
from openai import OpenAI

# 解析命令行参数
parser = argparse.ArgumentParser(description='生成小红书风格的心理学内容')
parser.add_argument('--count', type=int, default=5, help='要处理的记录数量，默认为5')
parser.add_argument('--start', type=int, default=0, help='起始记录索引，默认为0')
parser.add_argument('--output', type=str, default='心理学小红书内容_AI生成.xlsx', help='输出文件名')
args = parser.parse_args()

# API配置
API_KEY = "sk-ywZ3xdgLOUkWtQXB7FlYwhhN7niD25bDy3AFejlnCfDd9TsZ"
BASE_URL = "http://aigc-api.apps-hangyan.danlu.netease.com/v1"
client = OpenAI(api_key=API_KEY, base_url=BASE_URL)

# 读取Excel文件
excel_file = "心理学课程内容.xlsx"
output_file = args.output

try:
    df = pd.read_excel(excel_file)
    print(f"成功读取Excel文件: {excel_file}，共{len(df)}条记录")
    
    # 设置处理的记录数量和起始索引
    start_index = args.start
    process_count = min(args.count, len(df) - start_index)
    end_index = start_index + process_count
    
    print(f"将处理第{start_index+1}到第{end_index}条记录，共{process_count}条")
    
    # 系统提示词
    system_prompt = (
        "我是一个小红书心理学图文博主，专门将心理学知识转化为适合小红书平台的优质内容。"
        "我擅长提取核心知识点，去除个人化表述，形成结构化的知识讲解，并增加对读者的启发。"
    )
    
    # 处理每条记录
    for i in range(start_index, end_index):
        title = df.iloc[i]['课程标题']
        content = df.iloc[i]['课程文章内容']
        
        print(f"\n处理第{i+1}/{len(df)}条记录: {title}")
        
        try:
            # 构建用户提示词
            user_prompt = f"""
            我是一个小红书心理学图文博主，请基于以下内容做出下处理：

            课程标题：{title}
            课程内容：{content[:800]}...

            处理要求：
            1、提取核心知识点，去掉讲述人口吻和老师个人信息
            2、形成结构化小红书知识讲解内容并增加对人的启发
            3、生成符合小红书表达的格式和符号等
            4、总结封面主标题和副标题

            具体要求：
            - 封面主标题（15-25字）：吸引眼球，使用疑问句或感叹句，包含emoji
            - 副标题（20-35字）：补充说明或引发思考，语气亲切自然
            - 内页文字内容（480-520字）：
              * 开头要有吸引人的hook
              * 用生活化例子解释心理学概念
              * 分段清晰，每段2-3句话
              * 包含2-3个实用知识点
              * 语言口语化，像和朋友聊天
              * 适当使用emoji增加趣味性
              * 结尾有行动建议或互动问题
              * 加上相关话题标签

            请按以下格式输出：
            封面主标题：xxx
            副标题：xxx
            内页文字：
            xxx
            """
            
            # 调用API
            response = client.chat.completions.create(
                model="Doubao-Seed-1.6-thinking-250615",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                temperature=0.7,
            )
            
            response_text = response.choices[0].message.content
            print("AI生成内容成功")
            
            # 解析响应
            lines = response_text.split('\n')
            main_title = ''
            subtitle = ''
            content_text = ''
            current_section = None
            
            for line in lines:
                if line.startswith('封面主标题：'):
                    main_title = line.replace('封面主标题：', '').strip()
                    current_section = 'main_title'
                elif line.startswith('副标题：'):
                    subtitle = line.replace('副标题：', '').strip()
                    current_section = 'subtitle'
                elif line.startswith('内页文字：') or line.startswith('内页文字内容：'):
                    current_section = 'content'
                elif current_section == 'content':
                    content_text += line + '\n'
            
            # 更新DataFrame
            df.at[i, '封面主标题'] = main_title
            df.at[i, '副标题'] = subtitle
            df.at[i, '内页文字内容'] = content_text.strip()
            
            # 每次处理完一条记录后保存一次，防止中途出错丢失数据
            df.to_excel(output_file, index=False)
            print(f"已保存到{output_file}")
            
            # 避免API请求过快
            if i < end_index - 1:
                print("等待2秒...")
                time.sleep(2)
                
        except Exception as e:
            print(f"处理记录时出错: {e}")
            print("使用本地生成器生成内容...")
            
            # 使用本地生成器
            generator = XiaohongshuContentGenerator()
            result = generator.generate_content(title, content)
            
            # 更新DataFrame
            df.at[i, '封面主标题'] = result['封面主标题']
            df.at[i, '副标题'] = result['副标题']
            df.at[i, '内页文字内容'] = result['内页文字内容']
            
            # 保存
            df.to_excel(output_file, index=False)
            print(f"已使用本地生成器生成内容并保存到{output_file}")
    
    print(f"\n处理完成！共处理了{process_count}条记录，结果已保存到{output_file}")
    
except Exception as e:
    print(f"处理Excel文件时出错: {e}")
    print("尝试使用本地生成器...")
    
    # 使用本地生成器
    generator = XiaohongshuContentGenerator()
    example_title = "001|总论：心理学是干什么的？"
    example_content = "心理学是研究人类心理和行为的科学，它帮助我们理解自己和他人的思想、情感和行为。"
    
    result = generator.generate_content(example_title, example_content)
    print("本地生成的小红书内容:")
    print(f"封面主标题: {result['封面主标题']}")
    print(f"副标题: {result['副标题']}")
    print(f"内页文字:\n{result['内页文字内容']}")

if __name__ == "__main__":
    print("\n使用方法示例:")
    print("python ai_model.py --count 10 --start 5 --output 心理学小红书内容_AI生成_6-15.xlsx")
    print("这将处理第6到第15条记录，并保存到指定的Excel文件中")