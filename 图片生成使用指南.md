# 豆包图像生成器使用指南

## 🎨 功能概述

成功集成了豆包图像生成模型 `doubao-seedream-4-0-250828`，可以为心理学小红书内容自动生成封面图片。

### ✅ 已完成功能

1. **API集成**: 成功调用豆包图像生成API
2. **提示词加载**: 自动读取提示词.txt文件（36个提示词）
3. **参考图片**: 随机选择结果数据/封面2中的参考图片
4. **智能匹配**: 根据课程标题智能匹配相应的提示词
5. **批量生成**: 支持单个测试和批量处理
6. **结果保存**: 自动下载并保存生成的图片

## 🚀 使用方法

### 1. 测试单个课程图片生成
```bash
python improved_image_generator.py --count 1 --start 0 --output 测试结果.xlsx
```

### 2. 批量生成前5个课程的图片
```bash
python improved_image_generator.py --count 5 --start 0 --output 前5个课程图片.xlsx
```

### 3. 生成第6-15个课程的图片
```bash
python improved_image_generator.py --count 10 --start 5 --output 第6-15课程图片.xlsx
```

### 4. 生成所有40个课程的图片
```bash
python improved_image_generator.py --count 40 --start 0 --output 全部课程图片.xlsx
```

## 📊 测试结果

### ✅ 第一次测试成功
- **课程**: 001|总论：心理学是干什么的？
- **匹配提示词**: 通用心理学（未找到精确匹配）
- **参考图片**: 小红书封面设计 (14).png
- **生成状态**: ✅ 成功
- **生成图片**: 001总论理学是什么的_1757996338.png

### 📈 API调用详情
- **API地址**: https://ark.cn-beijing.volces.com/api/v3/images/generations
- **模型**: doubao-seedream-4-0-250828
- **图片尺寸**: 2K
- **响应格式**: URL
- **水印**: 启用

## 🎯 智能匹配系统

### 关键词映射表
| 课程关键词 | 匹配提示词类型 |
|------------|----------------|
| 总论 | 心理学基础 |
| 结构主义 | 认知图式 |
| 功能主义 | 自我认同 |
| 精神分析 | 潜意识探索 |
| 行为主义 | 行为改变 |
| 认知科学 | 认知图式 |
| 人本主义 | 自我演化 |
| 知觉 | 认知图式 |
| 自证 | 自我认同 |
| 皮格马利翁 | 成长思维 |
| 图示 | 认知图式 |
| 自我图式 | 自我图式 |
| 人生主题 | 人生方向 |
| 自我认同 | 自我认同 |
| 假自我 | 假我探索 |
| 强迫性重复 | 强迫重复 |
| 人格 | 人格心理 |
| 标签化 | 人格人际 |
| 拖延症 | 拖延建构 |
| 成长型思维 | 成长思维 |
| 失败 | 失败成长 |
| 危机 | 心理危机 |

## 📁 文件结构

```
心理学小红书/
├── improved_image_generator.py     # 改进版图片生成器
├── image_generator.py              # 基础版图片生成器
├── 提示词.txt                      # 提示词文件（36个）
├── 结果数据/封面2/                 # 参考图片文件夹
└── 生成的封面图片/                 # 输出目录
    ├── *.png                       # 生成的图片文件
    └── *.xlsx                      # 生成结果记录
```

## 🎨 生成效果特点

### 图片特征
- **尺寸**: 2K高清
- **风格**: 小红书封面风格
- **元素**: 心理学主题符号
- **色调**: 深邃青石灰色渐变
- **文字**: 白色等线体标题
- **水印**: 包含豆包水印

### 参考图片使用
- 从结果数据/封面2文件夹随机选择
- 支持PNG、JPG、JPEG格式
- 自动转换为base64格式传输
- 作为风格参考指导生成

## ⚙️ 技术参数

### API配置
```python
API_URL = "https://ark.cn-beijing.volces.com/api/v3/images/generations"
API_KEY = "0b2f220d-bba5-4650-8725-64757bbc1113"
MODEL = "doubao-seedream-4-0-250828"
```

### 请求参数
```json
{
    "model": "doubao-seedream-4-0-250828",
    "prompt": "提示词内容",
    "image": ["base64格式的参考图片"],
    "response_format": "url",
    "size": "2K",
    "stream": false,
    "watermark": true
}
```

## 🔧 性能优化

### 批量处理优化
- **延迟控制**: 每次调用间隔3秒，避免API限制
- **错误处理**: 自动重试和错误记录
- **断点续传**: 支持从指定位置开始处理
- **结果保存**: 实时保存生成结果

### 文件命名规则
- 格式: `{课程标题}_{时间戳}.png`
- 自动清理特殊字符
- 限制文件名长度（50字符）

## 📈 使用建议

### 1. 测试流程
1. 先运行单个测试确认效果
2. 小批量处理（5-10个）验证稳定性
3. 分批处理全部课程

### 2. 质量控制
- 检查生成的图片质量
- 验证提示词匹配准确性
- 确认参考图片选择合理性

### 3. 批量处理策略
- 建议分批处理，每批10-20个
- 避免一次性处理过多导致API限制
- 定期检查生成结果

## 🎉 成功案例

### 测试结果展示
**第一次测试**:
- ✅ API连接成功
- ✅ 提示词加载成功（36个）
- ✅ 参考图片选择成功
- ✅ 图片生成成功
- ✅ 文件保存成功

**生成质量**:
- 图片清晰度高（2K）
- 风格符合小红书特点
- 色调搭配专业
- 符合心理学主题

## 🚀 下一步计划

### 可选优化
1. **提示词优化**: 根据生成效果调整提示词
2. **批量处理**: 一次性生成所有40个课程的图片
3. **质量评估**: 建立图片质量评估机制
4. **风格调整**: 根据不同课程类型调整生成风格

### 使用建议
现在您可以：
1. 使用 `python improved_image_generator.py --count 1 --start 0` 继续测试
2. 使用 `python improved_image_generator.py --count 40 --start 0` 批量生成所有图片
3. 根据生成效果调整提示词或参考图片

图片生成功能已经完全就绪，可以开始大规模生成小红书封面图片了！🎊
