#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import pandas as pd
import os
import random
import base64
from pathlib import Path
import time
import argparse

class ImprovedDoubaoImageGenerator:
    def __init__(self):
        self.api_url = "https://ark.cn-beijing.volces.com/api/v3/images/generations"
        self.api_key = "0b2f220d-bba5-4650-8725-64757bbc1113"
        self.model = "doubao-seedream-4-0-250828"
        
        # 创建输出目录
        self.output_dir = Path("生成的封面图片")
        self.output_dir.mkdir(exist_ok=True)
        
        # 课程标题关键词映射
        self.keyword_mapping = {
            '总论': '心理学基础',
            '结构主义': '认知图式',
            '功能主义': '自我认同',
            '精神分析': '潜意识探索',
            '行为主义': '行为改变',
            '认知科学': '认知图式',
            '人本主义': '自我演化',
            '现状': '心理学基础',
            '实证': '心理学基础',
            '主线': '心理学基础',
            '稳态': '心理稳态',
            '知觉': '认知图式',
            '自证': '自我认同',
            '皮格马利翁': '成长思维',
            '图示': '认知图式',
            '自我图式': '自我图式',
            '人生主题': '人生方向',
            '人生无方向': '人生方向',
            '自我认同': '自我认同',
            '假自我': '假我探索',
            '自我稳定': '自我稳定',
            '强迫性重复': '强迫重复',
            '人格': '人格心理',
            '人格稳定': '人格稳定',
            '标签化': '人格人际',
            '关系中的人格': '关系人格',
            '语言建构': '语言建构',
            '分歧': '观点分歧',
            '拖延症': '拖延建构',
            '小结': '心理稳态',
            '改变': '时间改变',
            '成长型思维': '成长思维',
            '失败': '失败成长',
            '图式演化': '图式演化',
            '自我演化': '自我演化',
            '道德演化': '道德能力',
            '仪式': '仪式成长',
            '自主性': '青春自主',
            '演化停滞': '舒适圈',
            '危机': '心理危机'
        }
        
    def load_prompts(self, prompt_file="提示词.txt"):
        """加载提示词文件"""
        prompts = {}
        if not os.path.exists(prompt_file):
            print(f"❌ 提示词文件不存在: {prompt_file}")
            return prompts
            
        try:
            with open(prompt_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 解析提示词文件
            sections = content.split('\n\n')
            for section in sections:
                lines = section.strip().split('\n')
                if len(lines) >= 2:
                    title = lines[0].strip()
                    prompt = lines[1].replace('提示词：', '').strip()
                    if title and prompt:
                        prompts[title] = prompt
                        
            print(f"✅ 成功加载 {len(prompts)} 个提示词")
            return prompts
            
        except Exception as e:
            print(f"❌ 加载提示词文件失败: {e}")
            return prompts
    
    def get_random_reference_image(self, ref_dir="结果数据/封面2"):
        """从参考图片文件夹中随机选择一张图片"""
        ref_path = Path(ref_dir)
        if not ref_path.exists():
            print(f"❌ 参考图片目录不存在: {ref_dir}")
            return None
            
        # 获取所有图片文件
        image_files = list(ref_path.glob("*.png")) + list(ref_path.glob("*.jpg")) + list(ref_path.glob("*.jpeg"))
        
        if not image_files:
            print(f"❌ 参考图片目录中没有找到图片文件: {ref_dir}")
            return None
            
        # 随机选择一张图片
        selected_image = random.choice(image_files)
        print(f"📸 选择参考图片: {selected_image.name}")
        return str(selected_image)
    
    def image_to_base64_url(self, image_path):
        """将本地图片转换为base64 URL格式"""
        try:
            with open(image_path, 'rb') as image_file:
                image_data = image_file.read()
                base64_data = base64.b64encode(image_data).decode('utf-8')
                
                # 判断图片格式
                if image_path.lower().endswith('.png'):
                    mime_type = 'image/png'
                elif image_path.lower().endswith(('.jpg', '.jpeg')):
                    mime_type = 'image/jpeg'
                else:
                    mime_type = 'image/png'  # 默认
                    
                return f"data:{mime_type};base64,{base64_data}"
                
        except Exception as e:
            print(f"❌ 图片转换失败: {e}")
            return None
    
    def find_matching_prompt(self, course_title, prompts):
        """智能匹配课程标题与提示词"""
        # 清理课程标题
        title_clean = course_title.replace('|', ' ').replace('：', ' ').replace('？', '').replace('！', '')
        
        # 首先尝试关键词映射
        for keyword, mapped_key in self.keyword_mapping.items():
            if keyword in title_clean:
                if mapped_key in prompts:
                    return mapped_key, prompts[mapped_key]
        
        # 如果映射失败，尝试直接匹配
        for key in prompts.keys():
            if any(k in title_clean for k in key.split()):
                return key, prompts[key]
        
        # 都没匹配到，返回None
        return None, None
    
    def generate_image(self, prompt, reference_image_path=None, title="test"):
        """调用豆包API生成图片"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        # 构建请求数据
        data = {
            "model": self.model,
            "prompt": prompt,
            "response_format": "url",
            "size": "2K",
            "stream": False,
            "watermark": True
        }
        
        # 如果有参考图片，添加到请求中
        if reference_image_path:
            base64_url = self.image_to_base64_url(reference_image_path)
            if base64_url:
                data["image"] = [base64_url]
                print(f"📷 使用参考图片: {Path(reference_image_path).name}")
        
        try:
            print(f"🎨 开始生成图片: {title}")
            print(f"📝 提示词: {prompt[:100]}...")
            
            response = requests.post(self.api_url, headers=headers, json=data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ API调用成功")
                
                # 处理响应数据
                if 'data' in result and len(result['data']) > 0:
                    image_url = result['data'][0].get('url')
                    if image_url:
                        # 下载图片
                        image_filename = f"{title}_{int(time.time())}.png"
                        image_path = self.output_dir / image_filename
                        
                        img_response = requests.get(image_url, timeout=30)
                        if img_response.status_code == 200:
                            with open(image_path, 'wb') as f:
                                f.write(img_response.content)
                            print(f"💾 图片已保存: {image_path}")
                            return str(image_path)
                        else:
                            print(f"❌ 下载图片失败: {img_response.status_code}")
                    else:
                        print(f"❌ 响应中没有图片URL")
                else:
                    print(f"❌ 响应数据格式异常: {result}")
                    
            else:
                print(f"❌ API调用失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 生成图片时出错: {e}")
            
        return None
    
    def generate_cover_for_course(self, course_title, course_content, prompts):
        """为单个课程生成封面图片"""
        print(f"\n🎯 处理课程: {course_title}")
        
        # 智能匹配提示词
        matched_key, matched_prompt = self.find_matching_prompt(course_title, prompts)
        
        if not matched_prompt:
            # 如果没有匹配到，使用通用的心理学提示词
            matched_prompt = "深邃的青石灰色渐变背景，带有金属拉丝纹理和深蓝色光晕。中央图像是一个精致的心理学符号造型。顶部是深炭灰色磨砂边框，'每天一点心理学'为白色细等线体。主标题使用加粗白色等线体，副标题使用浅灰蓝色常规体。右下角有极简的白色引号图标。"
            matched_key = "通用心理学"
            print(f"⚠️ 未找到匹配的提示词，使用通用模板")
        else:
            print(f"✅ 匹配到提示词: {matched_key}")
        
        # 获取随机参考图片
        reference_image = self.get_random_reference_image()
        
        # 生成安全的文件名
        safe_title = "".join(c for c in course_title if c.isalnum() or c in (' ', '-', '_')).strip()[:50]
        
        # 生成图片
        generated_image = self.generate_image(matched_prompt, reference_image, safe_title)
        
        return {
            'course_title': course_title,
            'matched_prompt_key': matched_key,
            'prompt': matched_prompt,
            'reference_image': reference_image,
            'generated_image': generated_image,
            'success': generated_image is not None
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='豆包图像生成器')
    parser.add_argument('--count', type=int, default=1, help='要处理的记录数量，默认为1')
    parser.add_argument('--start', type=int, default=0, help='起始记录索引，默认为0')
    parser.add_argument('--output', type=str, default='图片生成结果.xlsx', help='输出文件名')
    args = parser.parse_args()
    
    print("🎨 改进版豆包图像生成器")
    print("=" * 60)
    
    # 初始化生成器
    generator = ImprovedDoubaoImageGenerator()
    
    # 加载提示词
    prompts = generator.load_prompts()
    if not prompts:
        print("❌ 无法加载提示词，退出程序")
        return
    
    # 读取课程数据
    excel_file = "心理学小红书内容_Doubao批量生成.xlsx"
    if not os.path.exists(excel_file):
        excel_file = "心理学课程内容.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ 找不到课程数据文件")
        return
    
    try:
        df = pd.read_excel(excel_file)
        print(f"✅ 成功读取课程数据: {len(df)} 条记录")
        
        # 设置处理范围
        start_index = args.start
        process_count = min(args.count, len(df) - start_index)
        end_index = start_index + process_count
        
        print(f"将处理第{start_index+1}到第{end_index}条记录，共{process_count}条")
        
        results = []
        success_count = 0
        
        for i in range(start_index, end_index):
            row = df.iloc[i]
            course_title = row['课程标题']
            course_content = row.get('课程文章内容', '')
            
            print(f"\n📚 处理第 {i+1}/{len(df)} 个课程")
            
            # 生成图片
            result = generator.generate_cover_for_course(course_title, course_content, prompts)
            results.append(result)
            
            if result['success']:
                success_count += 1
            
            # 添加延迟避免API限制
            if i < end_index - 1:
                print("⏱️ 等待3秒...")
                time.sleep(3)
        
        # 保存结果
        results_df = pd.DataFrame(results)
        results_file = generator.output_dir / args.output
        results_df.to_excel(results_file, index=False)
        
        print(f"\n🎉 图片生成完成!")
        print(f"   - 处理课程数: {process_count}")
        print(f"   - 成功生成: {success_count}")
        print(f"   - 失败数量: {process_count - success_count}")
        print(f"   - 结果文件: {results_file}")
        print(f"   - 图片目录: {generator.output_dir}")
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")

if __name__ == "__main__":
    main()
