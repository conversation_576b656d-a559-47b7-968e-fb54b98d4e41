import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import os

def format_doubao_generated_excel():
    """格式化Doubao生成的Excel文件"""
    input_file = '心理学小红书内容_Doubao批量生成.xlsx'
    output_file = '心理学小红书内容_Doubao批量生成_格式化.xlsx'
    
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        return False
    
    try:
        # 读取数据
        df = pd.read_excel(input_file)
        print(f"✅ 成功读取文件: {input_file}")
        print(f"📊 数据概览: {len(df)}行 x {len(df.columns)}列")
        
        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Doubao生成小红书内容"
        
        # 定义样式 - 使用Doubao主题色彩
        header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
        content_font = Font(name='微软雅黑', size=10)
        title_font = Font(name='微软雅黑', size=11, bold=True)
        
        header_fill = PatternFill(start_color='6C5CE7', end_color='6C5CE7', fill_type='solid')  # 紫色主题
        alt_fill = PatternFill(start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')  # 淡灰色
        
        center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        left_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
        
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 写入数据
        for r in dataframe_to_rows(df, index=False, header=True):
            ws.append(r)
        
        # 设置列宽
        column_widths = {
            'A': 35,  # 课程标题
            'B': 60,  # 课程文章内容
            'C': 35,  # 封面主标题
            'D': 40,  # 副标题
            'E': 80   # 内页文字内容
        }
        
        for col, width in column_widths.items():
            ws.column_dimensions[col].width = width
        
        # 格式化标题行
        for col in range(1, len(df.columns) + 1):
            cell = ws.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = center_alignment
            cell.border = thin_border
        
        # 格式化数据行
        for row in range(2, len(df) + 2):
            # 设置行高
            ws.row_dimensions[row].height = 150
            
            for col in range(1, len(df.columns) + 1):
                cell = ws.cell(row=row, column=col)
                
                # 设置边框
                cell.border = thin_border
                
                # 根据列设置不同的格式
                if col == 1:  # 课程标题
                    cell.font = title_font
                    cell.alignment = center_alignment
                elif col == 2:  # 课程文章内容
                    cell.font = content_font
                    cell.alignment = left_alignment
                elif col == 3:  # 封面主标题
                    cell.font = Font(name='微软雅黑', size=12, bold=True, color='6C5CE7')
                    cell.alignment = center_alignment
                elif col == 4:  # 副标题
                    cell.font = Font(name='微软雅黑', size=11, color='74B9FF')
                    cell.alignment = center_alignment
                elif col == 5:  # 内页文字内容
                    cell.font = content_font
                    cell.alignment = left_alignment
                
                # 交替行颜色
                if row % 2 == 0:
                    cell.fill = alt_fill
        
        # 冻结首行
        ws.freeze_panes = 'A2'
        
        # 添加筛选
        ws.auto_filter.ref = f"A1:E{len(df) + 1}"
        
        # 保存文件
        wb.save(output_file)
        print(f"✅ 格式化完成，已保存到: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ 格式化失败: {e}")
        return False

def validate_doubao_content():
    """验证Doubao生成内容的质量"""
    file_path = '心理学小红书内容_Doubao批量生成.xlsx'
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    try:
        df = pd.read_excel(file_path)
        print(f"\n🔍 Doubao生成内容质量分析")
        print("=" * 60)
        
        # 基本统计
        print(f"📊 基本信息:")
        print(f"   - 总记录数: {len(df)}")
        print(f"   - 列数: {len(df.columns)}")
        print(f"   - 列名: {list(df.columns)}")
        
        # 检查完整性
        print(f"\n📋 数据完整性:")
        for col in df.columns:
            null_count = df[col].isnull().sum()
            empty_count = (df[col] == '').sum()
            print(f"   - {col}: {null_count} 个空值, {empty_count} 个空字符串")
        
        # 内容长度分析
        print(f"\n📏 内容长度分析:")
        if '封面主标题' in df.columns:
            title_lengths = df['封面主标题'].str.len()
            print(f"   - 封面主标题: 平均 {title_lengths.mean():.1f} 字符 (范围: {title_lengths.min()}-{title_lengths.max()})")
        
        if '副标题' in df.columns:
            subtitle_lengths = df['副标题'].str.len()
            print(f"   - 副标题: 平均 {subtitle_lengths.mean():.1f} 字符 (范围: {subtitle_lengths.min()}-{subtitle_lengths.max()})")
        
        if '内页文字内容' in df.columns:
            content_lengths = df['内页文字内容'].str.len()
            print(f"   - 内页内容: 平均 {content_lengths.mean():.1f} 字符 (范围: {content_lengths.min()}-{content_lengths.max()})")
        
        # 检查小红书特征
        print(f"\n🎨 小红书风格特征:")
        if '封面主标题' in df.columns:
            emoji_pattern = r'[😀-🙏🌀-🗿🚀-🛿🇀-🇿✀-➿⚀-⚿]'
            titles_with_emoji = df['封面主标题'].str.contains(emoji_pattern, na=False).sum()
            print(f"   - 封面标题包含emoji: {titles_with_emoji}/{len(df)} ({titles_with_emoji/len(df)*100:.1f}%)")
        
        if '内页文字内容' in df.columns:
            hashtag_pattern = r'#\w+'
            content_with_hashtags = df['内页文字内容'].str.contains(hashtag_pattern, na=False).sum()
            print(f"   - 内页内容包含话题标签: {content_with_hashtags}/{len(df)} ({content_with_hashtags/len(df)*100:.1f}%)")
        
        # 显示几个示例
        print(f"\n📝 Doubao生成内容示例:")
        for i in range(min(3, len(df))):
            print(f"\n--- 示例 {i+1} ---")
            print(f"课程: {df.iloc[i]['课程标题']}")
            if '封面主标题' in df.columns:
                print(f"封面主标题: {df.iloc[i]['封面主标题']}")
            if '副标题' in df.columns:
                print(f"副标题: {df.iloc[i]['副标题']}")
            if '内页文字内容' in df.columns:
                content_preview = df.iloc[i]['内页文字内容'][:150] + "..." if len(df.iloc[i]['内页文字内容']) > 150 else df.iloc[i]['内页文字内容']
                print(f"内页内容预览: {content_preview}")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")

def compare_models():
    """对比GPT-4和Doubao生成的内容"""
    gpt4_file = '心理学小红书内容_AI批量生成.xlsx'
    doubao_file = '心理学小红书内容_Doubao批量生成.xlsx'
    
    print(f"\n🔄 模型对比分析")
    print("=" * 60)
    
    try:
        if os.path.exists(gpt4_file) and os.path.exists(doubao_file):
            df_gpt4 = pd.read_excel(gpt4_file)
            df_doubao = pd.read_excel(doubao_file)
            
            print(f"📊 基本对比:")
            print(f"   - GPT-4记录数: {len(df_gpt4)}")
            print(f"   - Doubao记录数: {len(df_doubao)}")
            
            # 长度对比
            if '封面主标题' in df_gpt4.columns and '封面主标题' in df_doubao.columns:
                gpt4_title_len = df_gpt4['封面主标题'].str.len().mean()
                doubao_title_len = df_doubao['封面主标题'].str.len().mean()
                print(f"\n📏 内容长度对比:")
                print(f"   - GPT-4封面标题平均长度: {gpt4_title_len:.1f} 字符")
                print(f"   - Doubao封面标题平均长度: {doubao_title_len:.1f} 字符")
            
            if '内页文字内容' in df_gpt4.columns and '内页文字内容' in df_doubao.columns:
                gpt4_content_len = df_gpt4['内页文字内容'].str.len().mean()
                doubao_content_len = df_doubao['内页文字内容'].str.len().mean()
                print(f"   - GPT-4内页内容平均长度: {gpt4_content_len:.1f} 字符")
                print(f"   - Doubao内页内容平均长度: {doubao_content_len:.1f} 字符")
            
            # Emoji使用对比
            emoji_pattern = r'[😀-🙏🌀-🗿🚀-🛿🇀-🇿✀-➿⚀-⚿]'
            if '封面主标题' in df_gpt4.columns and '封面主标题' in df_doubao.columns:
                gpt4_emoji = df_gpt4['封面主标题'].str.contains(emoji_pattern, na=False).sum()
                doubao_emoji = df_doubao['封面主标题'].str.contains(emoji_pattern, na=False).sum()
                print(f"\n🎨 风格特征对比:")
                print(f"   - GPT-4 Emoji使用: {gpt4_emoji}/{len(df_gpt4)} ({gpt4_emoji/len(df_gpt4)*100:.1f}%)")
                print(f"   - Doubao Emoji使用: {doubao_emoji}/{len(df_doubao)} ({doubao_emoji/len(df_doubao)*100:.1f}%)")
        
        else:
            print("⚠️ 无法找到对比文件，请确保两个模型的结果文件都存在")
            
    except Exception as e:
        print(f"❌ 对比分析失败: {e}")

def main():
    """主函数"""
    print("🤖 Doubao模型批量生成结果处理")
    print("=" * 60)
    
    # 验证内容质量
    validate_doubao_content()
    
    # 格式化Excel
    print(f"\n🎯 开始格式化Excel文件...")
    success = format_doubao_generated_excel()
    
    # 模型对比
    compare_models()
    
    if success:
        print(f"\n🎉 处理完成！")
        print(f"📁 生成的文件:")
        print(f"   - 心理学小红书内容_Doubao批量生成.xlsx (原始文件)")
        print(f"   - 心理学小红书内容_Doubao批量生成_格式化.xlsx (格式化文件)")
        
        # 显示文件大小
        for filename in ['心理学小红书内容_Doubao批量生成.xlsx', '心理学小红书内容_Doubao批量生成_格式化.xlsx']:
            if os.path.exists(filename):
                size = os.path.getsize(filename) / 1024
                print(f"   - {filename}: {size:.1f} KB")

if __name__ == "__main__":
    main()
