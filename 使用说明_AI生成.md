# 心理学小红书内容生成工具使用说明

## 功能概述

本工具可以将心理学课程内容转换为小红书风格的图文内容，包括：

1. 吸引人的封面主标题
2. 有吸引力的副标题
3. 结构化的内页文字内容

工具提供两种生成方式：
- 使用OpenAI API生成高质量内容（需要API密钥）
- 使用本地模拟生成内容（不需要API密钥）

## 文件说明

- `ai_model.py`: 使用OpenAI API生成小红书内容的主脚本
- `xiaohongshu_generator.py`: 小红书内容生成器类，支持API调用和本地生成
- `enhance_xiaohongshu_content.py`: 使用本地模拟方式生成小红书内容的脚本
- `心理学课程内容.xlsx`: 原始心理学课程内容Excel文件

## 使用方法

### 1. 使用OpenAI API生成内容

```bash
python ai_model.py --count <数量> --start <起始索引> --output <输出文件名>
```

参数说明：
- `--count`: 要处理的记录数量，默认为5
- `--start`: 起始记录索引（从0开始），默认为0
- `--output`: 输出文件名，默认为"心理学小红书内容_AI生成.xlsx"

示例：
```bash
# 处理前5条记录
python ai_model.py

# 处理第6到第15条记录
python ai_model.py --count 10 --start 5 --output 心理学小红书内容_AI生成_6-15.xlsx

# 处理所有记录
python ai_model.py --count 40 --start 0 --output 心理学小红书内容_AI生成_全部.xlsx
```

### 2. 使用本地模拟生成内容

如果没有OpenAI API密钥，或者API调用失败，可以使用本地模拟方式生成内容：

```bash
python enhance_xiaohongshu_content.py
```

本地生成的内容会保存到"心理学小红书内容_增强版.xlsx"文件中。

## 注意事项

1. 使用OpenAI API需要有效的API密钥，请在`ai_model.py`文件中配置
2. API生成的内容质量更高，但需要支付API使用费用
3. 本地生成的内容质量较简单，但不需要API密钥和费用
4. 生成过程中可能会出现API调用失败的情况，脚本会自动切换到本地生成模式
5. 每次处理完一条记录后，结果会立即保存到Excel文件，防止中途出错丢失数据

## 输出文件格式

生成的Excel文件包含以下列：
- 课程标题：原始课程标题
- 课程文章内容：原始课程内容
- 封面主标题：生成的小红书封面主标题
- 副标题：生成的小红书副标题
- 内页文字内容：生成的小红书内页文字

## 自定义生成内容

如果需要自定义生成内容的风格或格式，可以修改以下文件：

1. `ai_model.py`中的`system_prompt`和`user_prompt`变量
2. `xiaohongshu_generator.py`中的`_build_prompt`和`_generate_simple`方法

## 故障排除

1. 如果遇到API调用错误，请检查API密钥是否正确
2. 如果Excel文件保存失败，请确保输出文件没有被其他程序占用
3. 如果生成的内容不符合预期，可以尝试调整提示词或使用不同的模型