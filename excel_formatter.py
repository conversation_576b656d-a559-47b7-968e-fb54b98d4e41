import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import os

class ExcelFormatter:
    def __init__(self):
        # 定义样式
        self.header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
        self.content_font = Font(name='微软雅黑', size=10)
        self.title_font = Font(name='微软雅黑', size=11, bold=True)
        
        self.header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
        self.alt_fill = PatternFill(start_color='F2F2F2', end_color='F2F2F2', fill_type='solid')
        
        self.center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        self.left_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
        
        self.thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
    
    def format_excel(self, df: pd.DataFrame, output_path: str):
        """格式化并保存Excel文件"""
        try:
            # 创建工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "心理学课程内容"
            
            # 写入数据
            for r in dataframe_to_rows(df, index=False, header=True):
                ws.append(r)
            
            # 设置列宽
            column_widths = {
                'A': 35,  # 课程标题
                'B': 60,  # 课程文章内容
                'C': 30,  # 封面主标题
                'D': 35,  # 副标题
                'E': 70   # 内页文字内容
            }
            
            for col, width in column_widths.items():
                ws.column_dimensions[col].width = width
            
            # 格式化标题行
            for col in range(1, len(df.columns) + 1):
                cell = ws.cell(row=1, column=col)
                cell.font = self.header_font
                cell.fill = self.header_fill
                cell.alignment = self.center_alignment
                cell.border = self.thin_border
            
            # 格式化数据行
            for row in range(2, len(df) + 2):
                # 设置行高
                ws.row_dimensions[row].height = 120
                
                for col in range(1, len(df.columns) + 1):
                    cell = ws.cell(row=row, column=col)
                    
                    # 设置边框
                    cell.border = self.thin_border
                    
                    # 根据列设置不同的格式
                    if col == 1:  # 课程标题
                        cell.font = self.title_font
                        cell.alignment = self.center_alignment
                    elif col == 2:  # 课程文章内容
                        cell.font = self.content_font
                        cell.alignment = self.left_alignment
                    elif col == 3:  # 封面主标题
                        cell.font = self.title_font
                        cell.alignment = self.center_alignment
                    elif col == 4:  # 副标题
                        cell.font = self.content_font
                        cell.alignment = self.center_alignment
                    elif col == 5:  # 内页文字内容
                        cell.font = self.content_font
                        cell.alignment = self.left_alignment
                    
                    # 交替行颜色
                    if row % 2 == 0:
                        cell.fill = self.alt_fill
            
            # 冻结首行
            ws.freeze_panes = 'A2'
            
            # 保存文件
            wb.save(output_path)
            print(f"格式化的Excel文件已保存: {output_path}")
            return True
            
        except Exception as e:
            print(f"Excel格式化失败: {e}")
            return False

def main():
    """主函数：读取CSV并转换为格式化的Excel"""
    csv_path = '心理学课程内容.csv'
    excel_path = '心理学课程内容_格式化.xlsx'
    
    if not os.path.exists(csv_path):
        print(f"CSV文件不存在: {csv_path}")
        return
    
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_path, encoding='utf-8-sig')
        print(f"成功读取CSV文件，共{len(df)}行数据")
        
        # 创建格式化器并处理
        formatter = ExcelFormatter()
        success = formatter.format_excel(df, excel_path)
        
        if success:
            print("Excel文件格式化完成！")
            print(f"文件包含以下列：{list(df.columns)}")
            print(f"总共处理了{len(df)}个课程")
        
    except Exception as e:
        print(f"处理过程中出错: {e}")

if __name__ == "__main__":
    main()
